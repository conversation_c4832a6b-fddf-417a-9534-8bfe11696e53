#!/usr/bin/env python
"""
Test script for the comprehensive workflow simulation system.

This script tests the new workflow simulation functionality and data consistency fixes.

Usage:
    python test_workflow_simulation.py
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.insert(0, '/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/CommandNetSolutions')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings')
django.setup()

from django.core.management import call_command
from campaigns.models import Campaign
from campaigns.services.campaign_data_service import CampaignDataService


def test_workflow_simulation():
    """Test the workflow simulation system."""
    print("=" * 60)
    print("TESTING WORKFLOW SIMULATION SYSTEM")
    print("=" * 60)
    
    try:
        # Test 1: Create a test campaign and run simulation
        print("\n1. Creating test campaign and running simulation...")
        call_command('simulate_workflows', '--create-campaign', '--accounts', '25', '--delay', '0.05')
        print("✓ Campaign creation and simulation completed")
        
        # Test 2: Get the latest campaign and test data service
        print("\n2. Testing CampaignDataService...")
        latest_campaign = Campaign.objects.order_by('-created_at').first()
        if latest_campaign:
            data_service = CampaignDataService(latest_campaign)
            stats = data_service.get_comprehensive_stats()
            
            print(f"✓ Campaign: {latest_campaign.name}")
            print(f"✓ Total accounts: {stats['accounts']['total']}")
            print(f"✓ Analyzed accounts: {stats['accounts']['analyzed']}")
            print(f"✓ Whitelisted accounts: {stats['accounts']['whitelisted']}")
            print(f"✓ Conversion rate: {stats['rates']['conversion_rate']}%")
            print(f"✓ Recent discoveries: {len(stats['recent']['discoveries'])}")
            print(f"✓ Recent analyzed: {len(stats['recent']['analyzed'])}")
            print(f"✓ Recent whitelisted: {len(stats['recent']['whitelisted'])}")
        else:
            print("✗ No campaigns found")
            return False
        
        # Test 3: Test individual stage simulations
        print("\n3. Testing individual stage simulations...")
        
        # Test Stage 2 simulation
        call_command('simulate_workflows', '--campaign-id', str(latest_campaign.id), '--stage', '2', '--delay', '0.02')
        print("✓ Stage 2 simulation completed")
        
        # Test Stage 3 simulation
        call_command('simulate_workflows', '--campaign-id', str(latest_campaign.id), '--stage', '3', '--delay', '0.02')
        print("✓ Stage 3 simulation completed")
        
        # Test 4: Verify data consistency
        print("\n4. Testing data consistency...")
        updated_stats = data_service.get_comprehensive_stats()
        
        # Check if data is consistent
        if updated_stats['accounts']['total'] > 0:
            print("✓ Account data is present")
        else:
            print("✗ No account data found")
            return False
        
        if updated_stats['accounts']['whitelisted'] > 0:
            print("✓ Whitelist data is present")
        else:
            print("✗ No whitelist data found")
            return False
        
        # Test 5: Test stage-specific stats
        print("\n5. Testing stage-specific statistics...")
        stage_1_stats = data_service.get_stage_specific_stats(1)
        stage_2_stats = data_service.get_stage_specific_stats(2)
        stage_3_stats = data_service.get_stage_specific_stats(3)
        
        print(f"✓ Stage 1 - Discovered: {stage_1_stats.get('total_discovered', 0)}")
        print(f"✓ Stage 2 - Analysis rate: {stage_2_stats.get('analysis_rate', 0):.1f}%")
        print(f"✓ Stage 3 - Conversion rate: {stage_3_stats.get('conversion_rate', 0):.1f}%")
        
        print("\n" + "=" * 60)
        print("✓ ALL TESTS PASSED - WORKFLOW SIMULATION SYSTEM IS WORKING")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n✗ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_tag_search_fixes():
    """Test the tag search functionality fixes."""
    print("\n" + "=" * 60)
    print("TESTING TAG SEARCH FIXES")
    print("=" * 60)
    
    try:
        from campaigns.models import DynamicTag, TagCategory, TagGroup
        from django.test import RequestFactory
        from campaigns.views.tag_views import TagSearchView
        
        # Create test data
        print("\n1. Creating test tag data...")
        
        # Create a tag category
        category = TagCategory.objects.create(
            name='Test Category',
            description='Test category for simulation',
            color='#FF5733'
        )
        
        # Create a global tag
        global_tag = DynamicTag.objects.create(
            name='Test Global Tag',
            description='Test global tag',
            is_global=True,
            category=category,
            pattern='test',
            field='bio'
        )
        
        # Create a tag group and add tag to it
        tag_group = TagGroup.objects.create(
            name='Test Group',
            description='Test tag group',
            is_global=True
        )
        
        group_tag = DynamicTag.objects.create(
            name='Test Group Tag',
            description='Test tag in group',
            is_global=False,  # Not global itself
            category=category,
            pattern='group',
            field='bio'
        )
        group_tag.tag_groups.add(tag_group)
        
        print("✓ Test tag data created")
        
        # Test tag search
        print("\n2. Testing tag search functionality...")
        
        # Get a test campaign
        campaign = Campaign.objects.order_by('-created_at').first()
        if not campaign:
            print("✗ No campaign found for testing")
            return False
        
        # Create request factory
        factory = RequestFactory()
        
        # Test search without query
        request = factory.get(f'/campaigns/{campaign.id}/tags/search/')
        view = TagSearchView()
        response = view.get(request, campaign.id)
        
        if response.status_code == 200:
            print("✓ Tag search view responds correctly")
        else:
            print(f"✗ Tag search view failed with status {response.status_code}")
            return False
        
        # Test search with query
        request = factory.get(f'/campaigns/{campaign.id}/tags/search/?search=test')
        response = view.get(request, campaign.id)
        
        if response.status_code == 200:
            print("✓ Tag search with query works")
        else:
            print(f"✗ Tag search with query failed")
            return False
        
        print("\n" + "=" * 60)
        print("✓ TAG SEARCH FIXES VERIFIED")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n✗ TAG SEARCH TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("COMPREHENSIVE WORKFLOW SIMULATION AND FIXES TEST")
    print("=" * 80)
    
    # Test workflow simulation
    simulation_success = test_workflow_simulation()
    
    # Test tag search fixes
    tag_search_success = test_tag_search_fixes()
    
    # Final results
    print("\n" + "=" * 80)
    print("FINAL TEST RESULTS:")
    print("=" * 80)
    print(f"Workflow Simulation: {'✓ PASS' if simulation_success else '✗ FAIL'}")
    print(f"Tag Search Fixes: {'✓ PASS' if tag_search_success else '✗ FAIL'}")
    
    if simulation_success and tag_search_success:
        print("\n🎉 ALL SYSTEMS WORKING CORRECTLY!")
        print("\nNext steps:")
        print("1. Run the Django server: python manage.py runserver")
        print("2. Navigate to campaigns and test the UI")
        print("3. Create campaigns and test workflow simulation")
        print("4. Test tag management functionality")
    else:
        print("\n❌ SOME TESTS FAILED - CHECK LOGS ABOVE")
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
