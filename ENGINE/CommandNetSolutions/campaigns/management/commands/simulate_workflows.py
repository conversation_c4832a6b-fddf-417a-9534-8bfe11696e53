"""
Comprehensive Workflow Simulation System for Campaign Testing

This management command implements a robust simulation system that replaces dummy data
with realistic workflow behavior that mirrors the actual PyFlow/Airflow pipeline
without requiring real Instagram API calls or live workflows.

Features:
- Realistic data generation following actual campaign workflow stages
- Proper Redis integration for caching and task state management
- Modular simulation scripts for each workflow stage
- Realistic timing delays to simulate actual workflow execution
- Proper error handling and logging for debugging
- Support for multiple concurrent campaign simulations

Usage:
    python manage.py simulate_workflows --campaign-id <uuid> [options]
    python manage.py simulate_workflows --create-campaign [options]
    python manage.py simulate_workflows --stage <stage_number> --campaign-id <uuid>
"""

import json
import logging
import random
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction
from django.conf import settings

# Import campaign models
from campaigns.models import (
    Campaign, LocationTarget, UsernameTarget, DynamicTag, TagGroup,
    TagCategory, CampaignTag, TagAnalysisResult, WorkflowExecution,
    WorkflowProgressUpdate, CampaignResult
)
from instagram.models import Accounts, WhiteListEntry

# Import services
from campaigns.services.redis_service import RedisService
from campaigns.services.workflow_progress_service import WorkflowProgressService

logger = logging.getLogger(__name__)


class WorkflowSimulator:
    """
    Core workflow simulation engine that handles realistic data generation
    and workflow execution simulation.
    """

    def __init__(self, campaign: Campaign, redis_service: RedisService):
        self.campaign = campaign
        self.redis_service = redis_service
        self.progress_service = WorkflowProgressService()

        # Realistic data pools for generation
        self.fitness_usernames = [
            'fitness_guru_2024', 'healthy_lifestyle_coach', 'gym_motivation_daily',
            'protein_power_meals', 'cardio_queen_fit', 'strength_training_pro',
            'yoga_mindfulness_zen', 'crossfit_warrior_strong', 'running_miles_daily',
            'bodybuilding_champion', 'pilates_core_strength', 'hiit_workout_beast'
        ]

        self.fitness_bios = [
            "💪 Personal trainer helping you reach your fitness goals",
            "🏃‍♀️ Marathon runner | Nutrition coach | Wellness advocate",
            "🧘‍♀️ Yoga instructor spreading mindfulness and strength",
            "🏋️‍♂️ Certified trainer | Meal prep expert | Motivation daily",
            "🥗 Healthy recipes | Workout tips | Transform your life",
            "🏆 Fitness competitor | Online coaching | Results guaranteed"
        ]

        self.locations_data = [
            {'city': 'Miami', 'country': 'United States', 'location_id': '*********'},
            {'city': 'Los Angeles', 'country': 'United States', 'location_id': '*********'},
            {'city': 'New York', 'country': 'United States', 'location_id': '*********'},
            {'city': 'London', 'country': 'United Kingdom', 'location_id': '*********'},
            {'city': 'Sydney', 'country': 'Australia', 'location_id': '*********'},
        ]

    def simulate_stage_1_discovery(self, num_accounts: int = 50, delay: float = 0.1) -> WorkflowExecution:
        """
        Simulate Stage 1: Account Discovery
        Generates realistic username collection from location/hashtag sources
        """
        logger.info(f"Starting Stage 1 simulation for campaign {self.campaign.id}")

        # Create workflow execution record
        workflow = WorkflowExecution.objects.create(
            id=uuid.uuid4(),
            campaign=self.campaign,
            workflow_name='account_discovery.pygraph',
            workflow_path='/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/account_discovery.pygraph',
            workflow_type='collection',
            status='running',
            start_time=timezone.now(),
            total_items=num_accounts
        )

        # Start progress tracking
        self.progress_service.start_tracking(workflow)

        # Set initial Redis status
        self.redis_service.set_workflow_status(str(workflow.id), {
            'status': 'running',
            'stage': 'discovery',
            'progress': 0,
            'message': 'Starting account discovery...'
        })

        try:
            # Simulate account discovery process
            discovered_accounts = []

            for i in range(num_accounts):
                # Generate realistic account data
                username = self._generate_realistic_username()
                account_data = self._generate_realistic_account_data(username)

                # Create account record
                account = Accounts.objects.create(
                    username=username,
                    full_name=account_data['full_name'],
                    bio=account_data['bio'],
                    followers=account_data['followers'],
                    following=account_data['following'],
                    number_of_posts=account_data['posts'],
                    account_type=account_data['account_type'],
                    is_verified=account_data['is_verified'],
                    interests=account_data['interests'],
                    locations=account_data['locations'],
                    campaign_id=str(self.campaign.id),
                    collection_date=timezone.now()
                )

                discovered_accounts.append(account)

                # Update progress
                progress = ((i + 1) / num_accounts) * 100
                workflow.update_progress(
                    processed_items=i + 1,
                    successful_items=i + 1,
                    failed_items=0
                )

                # Update Redis status
                self.redis_service.set_workflow_status(str(workflow.id), {
                    'status': 'running',
                    'stage': 'discovery',
                    'progress': progress,
                    'message': f'Discovered {i + 1}/{num_accounts} accounts',
                    'current_account': username
                })

                # Realistic delay
                time.sleep(delay)

            # Complete workflow
            workflow.status = 'completed'
            workflow.end_time = timezone.now()
            workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
            workflow.save()

            # Final Redis status
            self.redis_service.set_workflow_status(str(workflow.id), {
                'status': 'completed',
                'stage': 'discovery',
                'progress': 100,
                'message': f'Successfully discovered {num_accounts} accounts'
            })

            logger.info(f"Stage 1 simulation completed: {num_accounts} accounts discovered")
            return workflow

        except Exception as e:
            # Handle errors
            workflow.status = 'failed'
            workflow.end_time = timezone.now()
            workflow.save()

            self.redis_service.set_workflow_status(str(workflow.id), {
                'status': 'failed',
                'stage': 'discovery',
                'error': str(e),
                'message': f'Discovery failed: {str(e)}'
            })

            logger.error(f"Stage 1 simulation failed: {str(e)}")
            raise

    def simulate_stage_2_analysis(self, delay: float = 0.1) -> WorkflowExecution:
        """
        Simulate Stage 2: Account Analysis
        Simulates follower counts, engagement rates, bio analysis
        """
        logger.info(f"Starting Stage 2 simulation for campaign {self.campaign.id}")

        # Get accounts from Stage 1
        accounts = Accounts.objects.filter(campaign_id=str(self.campaign.id))
        if not accounts.exists():
            raise ValueError("No accounts found from Stage 1. Run discovery first.")

        # Create workflow execution record
        workflow = WorkflowExecution.objects.create(
            id=uuid.uuid4(),
            campaign=self.campaign,
            workflow_name='account_analysis.pygraph',
            workflow_path='/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/account_analysis.pygraph',
            workflow_type='analysis',
            status='running',
            start_time=timezone.now(),
            total_items=accounts.count()
        )

        # Start progress tracking
        self.progress_service.start_tracking(workflow)

        # Set initial Redis status
        self.redis_service.set_workflow_status(str(workflow.id), {
            'status': 'running',
            'stage': 'analysis',
            'progress': 0,
            'message': 'Starting account analysis...'
        })

        try:
            analyzed_count = 0

            for i, account in enumerate(accounts):
                # Simulate analysis process
                self._analyze_account(account)
                analyzed_count += 1

                # Update progress
                progress = ((i + 1) / accounts.count()) * 100
                workflow.update_progress(
                    processed_items=i + 1,
                    successful_items=analyzed_count,
                    failed_items=0
                )

                # Update Redis status
                self.redis_service.set_workflow_status(str(workflow.id), {
                    'status': 'running',
                    'stage': 'analysis',
                    'progress': progress,
                    'message': f'Analyzed {analyzed_count}/{accounts.count()} accounts',
                    'current_account': account.username
                })

                # Realistic delay
                time.sleep(delay)

            # Complete workflow
            workflow.status = 'completed'
            workflow.end_time = timezone.now()
            workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
            workflow.save()

            # Final Redis status
            self.redis_service.set_workflow_status(str(workflow.id), {
                'status': 'completed',
                'stage': 'analysis',
                'progress': 100,
                'message': f'Successfully analyzed {analyzed_count} accounts'
            })

            logger.info(f"Stage 2 simulation completed: {analyzed_count} accounts analyzed")
            return workflow

        except Exception as e:
            # Handle errors
            workflow.status = 'failed'
            workflow.end_time = timezone.now()
            workflow.save()

            self.redis_service.set_workflow_status(str(workflow.id), {
                'status': 'failed',
                'stage': 'analysis',
                'error': str(e),
                'message': f'Analysis failed: {str(e)}'
            })

            logger.error(f"Stage 2 simulation failed: {str(e)}")
            raise

    def simulate_stage_3_tagging(self, delay: float = 0.1) -> WorkflowExecution:
        """
        Simulate Stage 3: Tag Assignment
        Simulates automatic tag application based on predefined rules
        """
        logger.info(f"Starting Stage 3 simulation for campaign {self.campaign.id}")

        # Get accounts from previous stages
        accounts = Accounts.objects.filter(campaign_id=str(self.campaign.id))
        if not accounts.exists():
            raise ValueError("No accounts found from previous stages.")

        # Get or create campaign tags
        campaign_tags = self._ensure_campaign_tags()

        # Create workflow execution record
        workflow = WorkflowExecution.objects.create(
            id=uuid.uuid4(),
            campaign=self.campaign,
            workflow_name='tag_assignment.pygraph',
            workflow_path='/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/tag_assignment.pygraph',
            workflow_type='tagging',
            status='running',
            start_time=timezone.now(),
            total_items=accounts.count()
        )

        # Start progress tracking
        self.progress_service.start_tracking(workflow)

        # Set initial Redis status
        self.redis_service.set_workflow_status(str(workflow.id), {
            'status': 'running',
            'stage': 'tagging',
            'progress': 0,
            'message': 'Starting tag assignment...'
        })

        try:
            tagged_count = 0
            whitelisted_count = 0

            for i, account in enumerate(accounts):
                # Simulate tag assignment
                assigned_tags = self._assign_tags_to_account(account, campaign_tags)

                # Determine if account should be whitelisted
                if self._should_whitelist_account(account, assigned_tags):
                    self._create_whitelist_entry(account)
                    whitelisted_count += 1

                tagged_count += 1

                # Update progress
                progress = ((i + 1) / accounts.count()) * 100
                workflow.update_progress(
                    processed_items=i + 1,
                    successful_items=tagged_count,
                    failed_items=0
                )

                # Update Redis status
                self.redis_service.set_workflow_status(str(workflow.id), {
                    'status': 'running',
                    'stage': 'tagging',
                    'progress': progress,
                    'message': f'Tagged {tagged_count}/{accounts.count()} accounts, {whitelisted_count} whitelisted',
                    'current_account': account.username
                })

                # Realistic delay
                time.sleep(delay)

            # Create campaign result
            self._create_campaign_result(accounts.count(), whitelisted_count)

            # Complete workflow
            workflow.status = 'completed'
            workflow.end_time = timezone.now()
            workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
            workflow.save()

            # Final Redis status
            self.redis_service.set_workflow_status(str(workflow.id), {
                'status': 'completed',
                'stage': 'tagging',
                'progress': 100,
                'message': f'Successfully tagged {tagged_count} accounts, {whitelisted_count} whitelisted'
            })

            logger.info(f"Stage 3 simulation completed: {tagged_count} accounts tagged, {whitelisted_count} whitelisted")
            return workflow

        except Exception as e:
            # Handle errors
            workflow.status = 'failed'
            workflow.end_time = timezone.now()
            workflow.save()

            self.redis_service.set_workflow_status(str(workflow.id), {
                'status': 'failed',
                'stage': 'tagging',
                'error': str(e),
                'message': f'Tagging failed: {str(e)}'
            })

            logger.error(f"Stage 3 simulation failed: {str(e)}")
            raise

    def _generate_realistic_username(self) -> str:
        """Generate realistic Instagram username"""
        base_names = self.fitness_usernames + [
            'wellness_warrior', 'fit_life_coach', 'muscle_building_tips',
            'healthy_habits_daily', 'transformation_tuesday', 'workout_wednesday'
        ]

        username = random.choice(base_names)

        # Add random suffix for uniqueness
        suffix = random.choice(['', '_fit', '_coach', '_pro', f'_{random.randint(1, 999)}'])
        return f"{username}{suffix}"[:30]  # Instagram username limit

    def _generate_realistic_account_data(self, username: str) -> Dict[str, Any]:
        """Generate realistic account data for fitness niche"""
        return {
            'full_name': self._generate_full_name(),
            'bio': random.choice(self.fitness_bios),
            'followers': random.randint(1000, 50000),
            'following': random.randint(500, 2000),
            'posts': random.randint(50, 500),
            'account_type': random.choice(['personal', 'business', 'creator']),
            'is_verified': random.choice([True, False]) if random.random() > 0.9 else False,
            'interests': self._generate_interests(),
            'locations': [random.choice(self.locations_data)['city']]
        }

    def _generate_full_name(self) -> str:
        """Generate realistic full names"""
        first_names = ['Alex', 'Jordan', 'Taylor', 'Casey', 'Morgan', 'Riley', 'Avery', 'Quinn']
        last_names = ['Fitness', 'Strong', 'Healthy', 'Active', 'Wellness', 'Power', 'Energy']
        return f"{random.choice(first_names)} {random.choice(last_names)}"

    def _generate_interests(self) -> List[str]:
        """Generate realistic fitness-related interests"""
        all_interests = [
            'fitness', 'health', 'nutrition', 'wellness', 'gym', 'workout',
            'yoga', 'running', 'bodybuilding', 'crossfit', 'pilates', 'cardio'
        ]
        return random.sample(all_interests, random.randint(2, 5))

    def _analyze_account(self, account: Accounts):
        """Simulate account analysis by updating account data"""
        # Simulate engagement rate calculation
        if account.followers and account.followers > 0:
            # Generate realistic engagement data
            avg_likes = random.randint(int(account.followers * 0.01), int(account.followers * 0.05))
            avg_comments = random.randint(int(avg_likes * 0.02), int(avg_likes * 0.1))

            # Update account with analysis results (if fields exist)
            if hasattr(account, 'likes_count'):
                account.likes_count = avg_likes
            if hasattr(account, 'comments_count'):
                account.comments_count = avg_comments

            account.save()

    def _ensure_campaign_tags(self) -> List[CampaignTag]:
        """Ensure campaign has tags for simulation"""
        # Get existing campaign tags
        existing_tags = CampaignTag.objects.filter(campaign=self.campaign)

        if existing_tags.exists():
            return list(existing_tags)

        # Create fitness-related tags if none exist
        fitness_tag_names = ['Fitness', 'Health', 'Nutrition', 'Gym', 'Workout']
        campaign_tags = []

        for tag_name in fitness_tag_names:
            # Get or create dynamic tag
            dynamic_tag, created = DynamicTag.objects.get_or_create(
                name=tag_name,
                defaults={
                    'description': f'Tag for {tag_name.lower()} related content',
                    'is_global': True,
                    'pattern': tag_name.lower(),
                    'field': 'bio'
                }
            )

            # Create campaign tag
            campaign_tag = CampaignTag.objects.create(
                campaign=self.campaign,
                tag=dynamic_tag,
                is_required=random.choice([True, False])
            )
            campaign_tags.append(campaign_tag)

        return campaign_tags

    def _assign_tags_to_account(self, account: Accounts, campaign_tags: List[CampaignTag]) -> List[str]:
        """Simulate tag assignment based on account bio and interests"""
        assigned_tags = []

        for campaign_tag in campaign_tags:
            tag_name = campaign_tag.tag.name.lower()

            # Check if tag matches account bio or interests
            should_assign = False

            if account.bio and tag_name in account.bio.lower():
                should_assign = True
            elif account.interests and any(tag_name in interest.lower() for interest in account.interests):
                should_assign = True
            else:
                # Random assignment for simulation (30% chance)
                should_assign = random.random() < 0.3

            if should_assign:
                # Create tag analysis result
                TagAnalysisResult.objects.create(
                    campaign=self.campaign,
                    account_id=account.username,
                    tag_name=campaign_tag.tag.name,
                    matched=True,
                    confidence_score=random.uniform(0.7, 1.0),
                    analysis_data={'bio_match': True, 'interests_match': True}
                )
                assigned_tags.append(campaign_tag.tag.name)

        return assigned_tags

    def _should_whitelist_account(self, account: Accounts, assigned_tags: List[str]) -> bool:
        """Determine if account should be whitelisted based on tags and criteria"""
        # Get required tags for campaign
        required_tags = CampaignTag.objects.filter(
            campaign=self.campaign,
            is_required=True
        ).values_list('tag__name', flat=True)

        # Check if all required tags are assigned
        if required_tags:
            required_tags_set = set(required_tags)
            assigned_tags_set = set(assigned_tags)
            if not required_tags_set.issubset(assigned_tags_set):
                return False

        # Additional criteria for whitelisting
        if account.followers and account.followers < 1000:
            return False  # Minimum follower requirement

        if len(assigned_tags) < 2:
            return False  # Minimum tag requirement

        # Random factor for simulation (70% chance if criteria met)
        return random.random() < 0.7

    def _create_whitelist_entry(self, account: Accounts):
        """Create whitelist entry for account"""
        WhiteListEntry.objects.create(
            account=account,
            last_updated=timezone.now()
        )

    def _create_campaign_result(self, total_accounts: int, whitelisted_accounts: int):
        """Create campaign result summary"""
        CampaignResult.objects.create(
            campaign=self.campaign,
            total_accounts_found=total_accounts,
            total_accounts_processed=total_accounts,
            total_accounts_pending=0,
            analysis_duration=random.uniform(300, 1800),  # 5-30 minutes
            last_processed_at=timezone.now()
        )


class Command(BaseCommand):
    help = 'Comprehensive workflow simulation system for campaign testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--campaign-id',
            type=str,
            help='UUID of the campaign to simulate'
        )
        parser.add_argument(
            '--create-campaign',
            action='store_true',
            help='Create a new test campaign for simulation'
        )
        parser.add_argument(
            '--stage',
            type=int,
            choices=[1, 2, 3],
            help='Specific workflow stage to simulate (1=Discovery, 2=Analysis, 3=Tagging)'
        )
        parser.add_argument(
            '--accounts',
            type=int,
            default=50,
            help='Number of accounts to simulate (default: 50)'
        )
        parser.add_argument(
            '--delay',
            type=float,
            default=0.1,
            help='Delay between operations in seconds (default: 0.1)'
        )
        parser.add_argument(
            '--concurrent',
            action='store_true',
            help='Run multiple workflow stages concurrently'
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up existing simulation data before starting'
        )

    def handle(self, *args, **options):
        """Main command handler"""
        try:
            # Initialize Redis service
            redis_service = RedisService()

            # Handle campaign creation
            if options['create_campaign']:
                campaign = self._create_test_campaign()
                self.stdout.write(
                    self.style.SUCCESS(f'Created test campaign: {campaign.name} ({campaign.id})')
                )
                options['campaign_id'] = str(campaign.id)

            # Validate campaign ID
            if not options['campaign_id']:
                raise CommandError('Campaign ID is required. Use --campaign-id or --create-campaign')

            try:
                campaign = Campaign.objects.get(id=options['campaign_id'])
            except Campaign.DoesNotExist:
                raise CommandError(f'Campaign with ID {options["campaign_id"]} not found')

            # Initialize simulator
            simulator = WorkflowSimulator(campaign, redis_service)

            # Handle cleanup
            if options['cleanup']:
                self._cleanup_simulation_data(campaign)
                self.stdout.write(self.style.WARNING('Cleaned up existing simulation data'))

            # Handle specific stage simulation
            if options['stage']:
                self._simulate_specific_stage(simulator, options['stage'], options)
            else:
                # Run full workflow simulation
                self._simulate_full_workflow(simulator, options)

            self.stdout.write(
                self.style.SUCCESS(f'Workflow simulation completed for campaign {campaign.name}')
            )

        except Exception as e:
            logger.exception(f"Workflow simulation failed: {str(e)}")
            raise CommandError(f'Simulation failed: {str(e)}')

    def _create_test_campaign(self) -> Campaign:
        """Create a test campaign for simulation"""
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                is_superuser=True,
                is_staff=True
            )

        campaign = Campaign.objects.create(
            name=f'Fitness Simulation Campaign {timezone.now().strftime("%Y%m%d_%H%M%S")}',
            description='Automated simulation campaign for testing workflow functionality',
            status='draft',
            target_type='location',
            audience_type='followers',
            creator=admin_user
        )

        # Add location target
        LocationTarget.objects.create(
            campaign=campaign,
            city='Miami',
            country='United States',
            location_id='*********'
        )

        return campaign

    def _cleanup_simulation_data(self, campaign: Campaign):
        """Clean up existing simulation data"""
        # Remove existing accounts
        Accounts.objects.filter(campaign_id=str(campaign.id)).delete()

        # Remove workflow executions
        WorkflowExecution.objects.filter(campaign=campaign).delete()

        # Remove whitelist entries
        WhiteListEntry.objects.filter(account__campaign_id=str(campaign.id)).delete()

        # Remove campaign results
        CampaignResult.objects.filter(campaign=campaign).delete()

    def _simulate_specific_stage(self, simulator: WorkflowSimulator, stage: int, options: Dict):
        """Simulate a specific workflow stage"""
        if stage == 1:
            simulator.simulate_stage_1_discovery(
                num_accounts=options['accounts'],
                delay=options['delay']
            )
            self.stdout.write(self.style.SUCCESS('Stage 1 (Discovery) simulation completed'))

        elif stage == 2:
            # Check if Stage 1 data exists
            accounts_count = Accounts.objects.filter(campaign_id=str(simulator.campaign.id)).count()
            if accounts_count == 0:
                self.stdout.write(self.style.WARNING('No accounts found. Running Stage 1 first...'))
                simulator.simulate_stage_1_discovery(
                    num_accounts=options['accounts'],
                    delay=options['delay']
                )

            simulator.simulate_stage_2_analysis(delay=options['delay'])
            self.stdout.write(self.style.SUCCESS('Stage 2 (Analysis) simulation completed'))

        elif stage == 3:
            # Check if previous stages data exists
            accounts_count = Accounts.objects.filter(campaign_id=str(simulator.campaign.id)).count()
            if accounts_count == 0:
                self.stdout.write(self.style.WARNING('No accounts found. Running full workflow...'))
                self._simulate_full_workflow(simulator, options)
                return

            simulator.simulate_stage_3_tagging(delay=options['delay'])
            self.stdout.write(self.style.SUCCESS('Stage 3 (Tagging) simulation completed'))

    def _simulate_full_workflow(self, simulator: WorkflowSimulator, options: Dict):
        """Simulate the complete workflow"""
        self.stdout.write(self.style.SUCCESS('Starting full workflow simulation...'))

        # Stage 1: Discovery
        self.stdout.write('Running Stage 1: Account Discovery...')
        simulator.simulate_stage_1_discovery(
            num_accounts=options['accounts'],
            delay=options['delay']
        )

        # Stage 2: Analysis
        self.stdout.write('Running Stage 2: Account Analysis...')
        simulator.simulate_stage_2_analysis(delay=options['delay'])

        # Stage 3: Tagging
        self.stdout.write('Running Stage 3: Tag Assignment...')
        simulator.simulate_stage_3_tagging(delay=options['delay'])

        # Update campaign status
        simulator.campaign.status = 'completed'
        simulator.campaign.save()

        self.stdout.write(self.style.SUCCESS('Full workflow simulation completed!'))
