"""
Views for the campaigns app.
"""
import logging

# Import views from the views directory
from . import workflow_views
from . import account_views
from . import tag_views
from .dashboard_views import CampaignDashboardView
from .account_views import CampaignAccountsView, CampaignWhiteListView
from .tag_views import (
    CampaignTagListView, CampaignTagCreateView, CampaignTagUpdateView, CampaignTagDeleteView
)

logger = logging.getLogger(__name__)

# Import Django views and mixins
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.views import View
# Removed LoginRequiredMixin to disable authentication requirements
# from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy, reverse
from django.shortcuts import get_object_or_404, redirect
from django.contrib import messages
from django.http import JsonResponse, HttpResponseRedirect, HttpResponse
from django.db.models import Q

# Import models and forms
from campaigns.models import Campaign, LocationTarget, UsernameTarget, DynamicTag, TagGroup
from campaigns.forms import CampaignForm, DynamicTagForm
from django import forms

# Export views
__all__ = [
    'workflow_views',
    'account_views',
    'tag_views',
    'CampaignDashboardView',
    'CampaignAccountsView',
    'CampaignWhiteListView',
    'CampaignTagListView',
    'CampaignTagCreateView',
    'CampaignTagUpdateView',
    'CampaignTagDeleteView',
    'CheckTagExistsView',
]

# Define the actual view classes that will be used by the application
class CampaignListView(ListView):
    """
    View to list all campaigns.
    """
    model = Campaign
    template_name = 'campaigns/campaign_list.html'
    context_object_name = 'campaigns'

    def get_queryset(self):
        """
        Return the list of campaigns, filtered by status if requested.
        """
        queryset = Campaign.objects.all().order_by('-created_at')

        # Apply status filter if provided
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Apply search filter if provided
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        """
        Add additional context data.
        """
        context = super().get_context_data(**kwargs)
        context['status_filter'] = self.request.GET.get('status', '')
        context['search_query'] = self.request.GET.get('search', '')
        return context

class CampaignDetailView(DetailView):
    """
    View to display campaign details.
    """
    model = Campaign
    template_name = 'campaigns/campaign_detail.html'
    context_object_name = 'campaign'

    def get_context_data(self, **kwargs):
        """
        Add additional context data.
        """
        context = super().get_context_data(**kwargs)
        campaign = self.object

        # Add location targets
        context['location_targets'] = LocationTarget.objects.filter(campaign=campaign).order_by('country', 'city')

        # Add username targets
        context['username_targets'] = UsernameTarget.objects.filter(campaign=campaign).order_by('username')

        # Add workflow information if available
        if hasattr(campaign, 'workflow_executions'):
            context['workflow_executions'] = campaign.workflow_executions.all().order_by('-created_at')

        # Use the new CampaignDataService for consistent statistics
        try:
            from campaigns.services.campaign_data_service import CampaignDataService
            data_service = CampaignDataService(campaign)

            # Get comprehensive stats
            stats = data_service.get_comprehensive_stats()

            # Map to existing template variables for backward compatibility
            context['discovery_stats'] = {
                'total': stats['accounts']['total'],
                'processed': stats['accounts']['analyzed']
            }
            context['analysis_stats'] = {
                'white_listed': stats['accounts']['whitelisted'],
                'percentage': stats['rates']['conversion_rate']
            }

            # Add new comprehensive data
            context['campaign_stats'] = stats

            # Sync CampaignResult to ensure consistency
            data_service.sync_campaign_result()

        except Exception as e:
            logger.warning(f"Could not load campaign data service: {str(e)}")
            # Fallback to legacy service
            try:
                from campaigns.services.legacy.campaign_analysis_service import CampaignAnalysisService
                analysis_service = CampaignAnalysisService()
                context['analysis_stats'] = analysis_service.get_campaign_analysis_stats(campaign.id)
            except Exception as fallback_error:
                logger.error(f"Fallback service also failed: {str(fallback_error)}")
                context['analysis_stats'] = {'white_listed': 0, 'percentage': 0}

        # Add campaign tags
        from campaigns.models import CampaignTag
        context['campaign_tags'] = CampaignTag.objects.filter(campaign=campaign).select_related('tag')

        # Add campaign result for progress tracking
        from campaigns.models import CampaignResult
        result, created = CampaignResult.objects.get_or_create(campaign=campaign)
        context['result'] = result

        # Add recent discoveries for Stage 1
        from instagram.models import Accounts, WhiteListEntry
        recent_accounts = Accounts.objects.filter(
            campaign_id=str(campaign.id)
        ).order_by('-collection_date')[:5]

        # Create recent discoveries with source information
        recent_discoveries = []
        for account in recent_accounts:
            # Determine source based on campaign targets
            source = "Unknown"
            if campaign.location_targets.exists():
                # Check if this account was collected from a location
                location_target = campaign.location_targets.first()
                source = f"Location: {location_target.city}, {location_target.country}"
            elif campaign.username_targets.exists():
                # Check if this account was collected from a username target
                username_target = campaign.username_targets.first()
                source = f"Followers of: @{username_target.username}"

            recent_discoveries.append({
                'username': account.username,
                'source': source,
                'discovered_at': account.collection_date
            })

        context['recent_discoveries'] = recent_discoveries

        # Add recent analyzed accounts for Stage 2
        # Get accounts that have been analyzed (have whitelist entries)
        analyzed_usernames = WhiteListEntry.objects.filter(
            account__campaign_id=str(campaign.id)
        ).values_list('account__username', flat=True)

        recent_analyzed = Accounts.objects.filter(
            campaign_id=str(campaign.id),
            username__in=analyzed_usernames
        ).order_by('-collection_date')[:5]

        recent_analyzed_accounts = []
        for account in recent_analyzed:
            recent_analyzed_accounts.append({
                'username': account.username,
                'followers': account.followers,
                'analyzed_at': account.collection_date  # Using collection_date as proxy for analysis time
            })

        context['recent_analyzed_accounts'] = recent_analyzed_accounts

        # Add recent whitelisted accounts for Stage 3
        recent_whitelist = WhiteListEntry.objects.filter(
            account__campaign_id=str(campaign.id)
        ).select_related('account').order_by('-last_updated')[:5]  # Using last_updated for ordering

        recent_whitelisted_accounts = []
        for entry in recent_whitelist:
            recent_whitelisted_accounts.append({
                'username': entry.account.username,
                'followers': entry.account.followers,
                'whitelisted_at': entry.account.collection_date  # Using collection_date as proxy
            })

        context['recent_whitelisted_accounts'] = recent_whitelisted_accounts

        return context

class CampaignCreateView(CreateView):
    """
    View to create a new campaign.
    """
    model = Campaign
    form_class = CampaignForm
    template_name = 'campaigns/campaign_form.html'

    def get_success_url(self):
        """
        Return the URL to redirect to after successful form submission.
        """
        return reverse('campaigns:campaign_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        """
        Process the form submission.
        """
        # Set the creator to the current user if authenticated, otherwise set to None
        if self.request.user.is_authenticated:
            form.instance.creator = self.request.user
        # If not authenticated, creator will be None (make sure your model allows this)

        # Save the campaign
        response = super().form_valid(form)

        # Add success message
        messages.success(self.request, f"Campaign '{form.instance.name}' created successfully.")

        return response

class CampaignUpdateView(UpdateView):
    """
    View to update an existing campaign.
    """
    model = Campaign
    form_class = CampaignForm
    template_name = 'campaigns/campaign_form.html'

    def get_success_url(self):
        """
        Return the URL to redirect to after successful form submission.
        """
        return reverse('campaigns:campaign_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        """
        Process the form submission.
        """
        # Save the campaign
        response = super().form_valid(form)

        # Add success message
        messages.success(self.request, f"Campaign '{form.instance.name}' updated successfully.")

        return response

class CampaignDeleteView(DeleteView):
    """
    View to delete a campaign.
    """
    model = Campaign
    template_name = 'campaigns/campaign_confirm_delete.html'
    success_url = reverse_lazy('campaigns:campaign_list')

    def form_valid(self, form):
        """
        Delete the campaign and handle related data.
        This method is called when the form is valid (i.e., when the user confirms deletion).
        """
        campaign = self.get_object()
        name = campaign.name

        try:
            # Import required models
            from instagram.models import Accounts, WhiteListEntry
            from campaigns.models.workflow import WorkflowExecution

            # Count related data for user feedback
            accounts_to_update = Accounts.objects.filter(campaign_id=str(campaign.id))
            accounts_count = accounts_to_update.count()

            # Count workflow executions that will be deleted
            workflow_count = WorkflowExecution.objects.filter(campaign=campaign).count()

            # Handle related accounts by removing the campaign reference
            # instead of deleting the accounts (they might be used by other campaigns)
            accounts_to_update.update(campaign_id=None)

            # Delete related whitelist entries for these accounts
            WhiteListEntry.objects.filter(account__in=accounts_to_update).delete()

            # Now delete the campaign (workflow executions will be cascade deleted)
            response = super().form_valid(form)

            # Add success message with details
            message_parts = [f"Campaign '{name}' deleted successfully."]
            if accounts_count > 0:
                message_parts.append(f"{accounts_count} accounts were unlinked from this campaign.")
            if workflow_count > 0:
                message_parts.append(f"{workflow_count} workflow executions were also deleted.")

            messages.success(self.request, " ".join(message_parts))

            return response

        except Exception as e:
            # If there's still an error, provide a helpful message
            messages.error(
                self.request,
                f"Error deleting campaign '{name}': {str(e)}. Please contact support if this issue persists."
            )
            return redirect('campaigns:campaign_detail', pk=campaign.pk)

class AddLocationTargetView(View):
    """
    View to add a location target to a campaign.
    """
    def get(self, request, pk):
        """
        Display the form to add a location target.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        return render(request, 'campaigns/add_location_target.html', {'campaign': campaign})

    def post(self, request, pk):
        """
        Process the form submission.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        # Process form data here
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class AddLocationToTargetView(View):
    """
    View to add a location to a campaign target.
    """
    def post(self, request, pk):
        """
        Process the form submission.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        # Process form data here
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class AddUsernameTargetView(View):
    """
    View to add a username target to a campaign.
    """
    def get(self, request, pk):
        """
        Display the form to add a username target.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        return render(request, 'campaigns/add_username_target.html', {'campaign': campaign})

    def post(self, request, pk):
        """
        Process the form submission.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        # Process form data here
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class RemoveLocationTargetView(View):
    """
    View to remove a location target from a campaign.
    """
    def post(self, request, campaign_pk, target_pk):
        """
        Process the form submission.
        """
        campaign = get_object_or_404(Campaign, pk=campaign_pk)
        target = get_object_or_404(LocationTarget, pk=target_pk, campaign=campaign)
        target.delete()
        messages.success(request, "Location target removed successfully.")
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class RemoveUsernameTargetView(View):
    """
    View to remove a username target from a campaign.
    """
    def post(self, request, campaign_pk, target_pk):
        """
        Process the form submission.
        """
        campaign = get_object_or_404(Campaign, pk=campaign_pk)
        target = get_object_or_404(UsernameTarget, pk=target_pk, campaign=campaign)
        target.delete()
        messages.success(request, "Username target removed successfully.")
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class LaunchCampaignView(View):
    """
    View to launch a campaign.
    """
    def post(self, request, pk):
        """
        Process the form submission.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        # Launch campaign logic here
        campaign.status = 'running'
        campaign.save()
        messages.success(request, f"Campaign '{campaign.name}' launched successfully.")
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class PauseCampaignView(View):
    """
    View to pause a campaign.
    """
    def post(self, request, pk):
        """
        Process the form submission.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        if campaign.can_pause():
            campaign.pause()
            messages.success(request, f"Campaign '{campaign.name}' paused successfully.")
        else:
            messages.error(request, f"Campaign '{campaign.name}' cannot be paused.")
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class ResumeCampaignView(View):
    """
    View to resume a campaign.
    """
    def post(self, request, pk):
        """
        Process the form submission.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        if campaign.can_resume():
            campaign.resume()
            messages.success(request, f"Campaign '{campaign.name}' resumed successfully.")
        else:
            messages.error(request, f"Campaign '{campaign.name}' cannot be resumed.")
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class StopCampaignView(View):
    """
    View to stop a campaign.
    """
    def post(self, request, pk):
        """
        Process the form submission.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        if campaign.can_stop():
            campaign.stop()
            messages.success(request, f"Campaign '{campaign.name}' stopped successfully.")
        else:
            messages.error(request, f"Campaign '{campaign.name}' cannot be stopped.")
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class ToggleFavoriteView(View):
    """
    View to toggle a campaign's favorite status.
    """
    def get(self, request, pk):
        """
        Process the request.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        campaign.is_favorite = not campaign.is_favorite
        campaign.save(update_fields=['is_favorite'])

        # Get the next URL from the query string or default to campaign detail
        next_url = request.GET.get('next', reverse('campaigns:campaign_detail', kwargs={'pk': campaign.pk}))

        # Add success message
        if campaign.is_favorite:
            messages.success(request, f"Campaign '{campaign.name}' added to favorites.")
        else:
            messages.success(request, f"Campaign '{campaign.name}' removed from favorites.")

        return redirect(next_url)

class ExportCampaignView(View):
    """
    View to export campaign data.
    """
    def get(self, request, pk):
        """
        Process the request and export campaign data.
        """
        campaign = get_object_or_404(Campaign, pk=pk)

        # Get export format from query parameters
        export_format = request.GET.get('format', 'csv')
        export_type = request.GET.get('type', 'accounts')

        # Import required modules for export
        import csv
        from django.http import HttpResponse
        from instagram.models import Accounts, WhiteListEntry
        from campaigns.models import TagAnalysisResult

        # Get the data based on export type
        if export_type == 'accounts':
            # Get accounts data
            accounts = Accounts.objects.filter(campaign_id=str(campaign.id))

            if export_format == 'csv':
                # Export as CSV
                response = HttpResponse(content_type='text/csv')
                response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_accounts.csv"'

                writer = csv.writer(response)
                writer.writerow([
                    'Username', 'Full Name', 'Bio', 'Followers', 'Following', 'Posts',
                    'Account Type', 'Verified', 'Collection Date', 'Interests', 'Locations'
                ])

                for account in accounts:
                    writer.writerow([
                        account.username,
                        account.full_name,
                        account.bio,
                        account.followers,
                        account.following,
                        account.number_of_posts,
                        account.account_type,
                        account.is_verified,
                        account.collection_date.strftime('%Y-%m-%d %H:%M:%S') if account.collection_date else '',
                        ', '.join(account.interests) if account.interests else '',
                        ', '.join(account.locations) if account.locations else ''
                    ])

                return response

            elif export_format == 'excel':
                # Export as Excel
                try:
                    import xlsxwriter
                    from io import BytesIO

                    # Create a workbook and add a worksheet
                    output = BytesIO()
                    workbook = xlsxwriter.Workbook(output)
                    worksheet = workbook.add_worksheet('Accounts')

                    # Add a bold format to use to highlight cells
                    bold = workbook.add_format({'bold': True})

                    # Write headers
                    headers = [
                        'Username', 'Full Name', 'Bio', 'Followers', 'Following', 'Posts',
                        'Account Type', 'Verified', 'Collection Date', 'Interests', 'Locations'
                    ]
                    for col, header in enumerate(headers):
                        worksheet.write(0, col, header, bold)

                    # Write data
                    for row, account in enumerate(accounts, 1):
                        worksheet.write(row, 0, account.username)
                        worksheet.write(row, 1, account.full_name or '')
                        worksheet.write(row, 2, account.bio or '')
                        worksheet.write(row, 3, account.followers or 0)
                        worksheet.write(row, 4, account.following or 0)
                        worksheet.write(row, 5, account.number_of_posts or 0)
                        worksheet.write(row, 6, account.account_type or '')
                        worksheet.write(row, 7, 'Yes' if account.is_verified else 'No')
                        worksheet.write(row, 8, account.collection_date.strftime('%Y-%m-%d %H:%M:%S') if account.collection_date else '')
                        worksheet.write(row, 9, ', '.join(account.interests) if account.interests else '')
                        worksheet.write(row, 10, ', '.join(account.locations) if account.locations else '')

                    # Adjust column widths
                    worksheet.set_column(0, 0, 20)  # Username
                    worksheet.set_column(1, 1, 25)  # Full Name
                    worksheet.set_column(2, 2, 30)  # Bio
                    worksheet.set_column(3, 5, 12)  # Followers, Following, Posts
                    worksheet.set_column(6, 6, 15)  # Account Type
                    worksheet.set_column(7, 7, 10)  # Verified
                    worksheet.set_column(8, 8, 20)  # Collection Date
                    worksheet.set_column(9, 10, 25)  # Interests, Locations

                    # Close the workbook
                    workbook.close()

                    # Create the response
                    response = HttpResponse(
                        output.getvalue(),
                        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )
                    response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_accounts.xlsx"'

                    return response

                except ImportError:
                    messages.error(request, "Excel export requires xlsxwriter package. Using CSV instead.")
                    return redirect(f"{request.path}?format=csv")

        elif export_type == 'whitelist':
            # Get whitelist entries
            whitelist_entries = WhiteListEntry.objects.filter(account__campaign_id=str(campaign.id))

            if export_format == 'csv':
                # Export as CSV
                response = HttpResponse(content_type='text/csv')
                response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_whitelist.csv"'

                writer = csv.writer(response)
                writer.writerow([
                    'Account', 'Tags', 'DM', 'Discover', 'Comment', 'Post Like', 'Favorite', 'Follow', 'Auto'
                ])

                for entry in whitelist_entries:
                    writer.writerow([
                        entry.account.username,
                        ','.join(entry.tags) if entry.tags else '',
                        entry.dm,
                        entry.discover,
                        entry.comment,
                        entry.post_like,
                        entry.favorite,
                        entry.follow,
                        entry.is_auto
                    ])

                return response

        # Default fallback - redirect with error message
        messages.error(request, f"Invalid export format or type.")
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class AnalyzeCampaignView(View):
    """
    View to analyze a campaign.
    """
    def get(self, request, pk):
        """
        Process the request.
        """
        campaign = get_object_or_404(Campaign, pk=pk)
        # Analysis logic here
        messages.success(request, f"Campaign '{campaign.name}' analysis started.")
        return redirect('campaigns:campaign_detail', pk=campaign.pk)

class DynamicTagListView(ListView):
    """
    View to list all dynamic tags.
    """
    model = DynamicTag
    template_name = 'campaigns/dynamic_tag_list.html'
    context_object_name = 'dynamic_tags'
    paginate_by = 10  # Add pagination

    def get_queryset(self):
        """
        Return the list of tags, filtered by category if requested.
        """
        queryset = DynamicTag.objects.all().order_by('name')

        # Apply category filter if provided
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__id=category)

        # Apply search filter if provided
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        """
        Add categories to the context.
        """
        context = super().get_context_data(**kwargs)
        # Add categories for the filter dropdown
        from campaigns.models import TagCategory
        context['categories'] = TagCategory.objects.all().order_by('name')
        return context

class DynamicTagCreateView(CreateView):
    """
    View to create a new dynamic tag.
    """
    model = DynamicTag
    template_name = 'campaigns/dynamic_tag_form.html'
    form_class = DynamicTagForm

    def get_form(self, form_class=None):
        """
        Get the form for this view.
        Add is_system field only for superusers.
        """
        form = super().get_form(form_class)
        # Add is_system field only for superusers
        if self.request.user.is_superuser:
            form.fields['is_system'] = forms.BooleanField(
                required=False,
                initial=False,
                help_text="If checked, this tag will be a system tag that cannot be deleted by users."
            )

        # Store the request in the form for later use
        form._request = self.request

        return form

    def form_valid(self, form):
        """
        Process the form submission.
        Set is_system field based on user permissions.
        """
        import logging
        import json
        import traceback
        import sys
        from django.shortcuts import redirect
        logger = logging.getLogger(__name__)

        # Set up console logging for immediate feedback
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        logger.setLevel(logging.DEBUG)

        logger.critical("========== STARTING TAG CREATION ==========")
        logger.critical(f"DynamicTagCreateView form_valid - form.cleaned_data: {form.cleaned_data}")

        # Log the request data for debugging
        logger.critical(f"Request POST data: {self.request.POST}")
        logger.critical(f"conditions_json: {self.request.POST.get('conditions_json', 'NOT FOUND')}")
        logger.critical(f"form_operation: {self.request.POST.get('form_operation', 'NOT FOUND')}")

        # Log form instance details
        logger.critical(f"Form instance pk: {form.instance.pk}")
        logger.critical(f"Form instance id: {getattr(form.instance, 'id', None)}")
        logger.critical(f"Is this a new tag? {'Yes' if not form.instance.pk else 'No'}")

        # Log the form action URL
        form_operation = self.request.POST.get('form_operation', 'unknown')
        if form_operation == 'create':
            logger.critical("This is a CREATE operation")
        elif form_operation == 'update':
            logger.critical("This is an UPDATE operation")
        else:
            logger.critical(f"Unknown form operation: {form_operation}")

        # Only allow superusers to create system tags
        if 'is_system' in form.cleaned_data and form.cleaned_data['is_system']:
            if not self.request.user.is_superuser:
                form.instance.is_system = False
                messages.warning(self.request, "Only administrators can create system tags.")
            else:
                form.instance.is_system = True
                messages.success(self.request, f"System tag '{form.instance.name}' created successfully.")

        # Ensure required fields have values
        if not form.instance.field:
            form.instance.field = 'bio'
            logger.warning("Field was empty, setting to 'bio'")

        if not form.instance.pattern:
            form.instance.pattern = '{}'  # Use empty JSON object instead of empty string
            logger.warning("Pattern was empty, setting to empty JSON object")

        if not form.instance.tag_type:
            form.instance.tag_type = 'keyword'
            logger.warning("Tag type was empty, setting to 'keyword'")

        # Set default values for any other required fields
        if not hasattr(form.instance, 'weight') or form.instance.weight is None:
            form.instance.weight = 1.0
            logger.warning("Weight was empty, setting to 1.0")

        try:
            # Get conditions_json from the request and add it to form.cleaned_data
            conditions_json = self.request.POST.get('conditions_json', '[]')
            logger.info(f"Processing conditions_json from request: {conditions_json}")

            # Make sure conditions_json is in form.cleaned_data
            if 'conditions_json' not in form.cleaned_data or not form.cleaned_data['conditions_json']:
                form.cleaned_data['conditions_json'] = conditions_json
                logger.info(f"Added conditions_json to form.cleaned_data: {conditions_json}")

            # Parse conditions to ensure they're valid
            try:
                conditions = json.loads(conditions_json)
                logger.info(f"Parsed conditions: {conditions}")

                # Add conditions to form.cleaned_data
                form.cleaned_data['conditions'] = conditions
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing conditions_json: {str(e)}")
                form.cleaned_data['conditions'] = []

            # Save the form first to create the tag
            logger.info("Saving form to create tag")
            tag = form.save(commit=True)  # Save the form and get the tag instance
            logger.info(f"Tag saved with ID: {tag.id}")

            # Now save the conditions
            if hasattr(form, 'save_conditions'):
                logger.info("Calling save_conditions method")
                success = form.save_conditions(tag)
                if success:
                    logger.info(f"Successfully saved conditions for tag '{tag.name}'")
                else:
                    logger.warning(f"Failed to save conditions for tag '{tag.name}'")
                    messages.warning(self.request, "Tag was created but there was an issue saving the conditions.")
            else:
                logger.warning("Form does not have save_conditions method")

            # Verify the tag was saved correctly
            from campaigns.models import DynamicTag
            try:
                # Force a database refresh to ensure we're getting the latest data
                saved_tag = DynamicTag.objects.get(id=tag.id)
                logger.info(f"Tag verification - Found tag with ID {saved_tag.id}, name: {saved_tag.name}")

                # Log all tags in the database for debugging
                all_tags = DynamicTag.objects.all()
                logger.info(f"All tags in database: {[t.name for t in all_tags]}")

                # Double-check that our tag is in the list
                if tag.id in [t.id for t in all_tags]:
                    logger.info(f"Tag {tag.id} confirmed in database")
                else:
                    logger.error(f"Tag {tag.id} NOT found in all_tags list!")

            except DynamicTag.DoesNotExist:
                logger.error(f"Tag verification failed - Could not find tag with ID {tag.id}")
                messages.error(self.request, "Error creating tag: Tag was not saved correctly.")
                return self.form_invalid(form)

            messages.success(self.request, f"Tag '{tag.name}' created successfully.")

            # Explicitly redirect to the tag list page
            logger.critical("========== REDIRECTING TO TAG LIST PAGE ==========")
            from django.http import HttpResponseRedirect
            from django.urls import reverse

            # Get the absolute URL for the tag list page
            tag_list_url = reverse('campaigns:dynamic_tag_list')
            logger.critical(f"Redirecting to tag list URL: {tag_list_url}")

            # Try a different approach to redirect
            try:
                # First, try using HttpResponseRedirect with the absolute URL
                logger.critical("Using HttpResponseRedirect for redirect")
                response = HttpResponseRedirect(tag_list_url)

                # Add debugging headers
                response['X-Debug-Info'] = 'Tag created successfully'
                response['X-Tag-ID'] = str(tag.id)
                response['X-Tag-Name'] = tag.name

                # Log the response for debugging
                logger.critical(f"Response created: {response}")
                logger.critical(f"Response status code: {response.status_code}")
                logger.critical(f"Response headers: {response.headers}")

                return response
            except Exception as e:
                logger.critical(f"Error during redirect: {str(e)}")
                logger.critical(traceback.format_exc())

                # As a fallback, try a simple redirect
                logger.critical("Falling back to simple redirect")
                return redirect('campaigns:dynamic_tag_list')
        except Exception as e:
            logger.exception(f"Error creating tag: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            messages.error(self.request, f"Error creating tag: {str(e)}")
            return self.form_invalid(form)

    def get_context_data(self, **kwargs):
        """
        Add existing conditions to the context.
        """
        context = super().get_context_data(**kwargs)

        # If we're editing an existing tag, get its conditions
        if self.object and self.object.pk:
            import json
            import logging
            logger = logging.getLogger(__name__)

            try:
                # Try to parse the pattern as JSON to get the rule ID
                pattern_data = json.loads(self.object.pattern)
                if 'rule_id' in pattern_data:
                    from campaigns.models import CampaignTagRule, CampaignTagCondition
                    rule_id = pattern_data.get('rule_id')

                    try:
                        # Get the rule and its conditions
                        rule = CampaignTagRule.objects.get(id=rule_id)
                        conditions = []

                        for condition in rule.conditions.all():
                            # Convert condition to a dict for the template
                            field_category = 'text'  # Default

                            # Determine field category based on field
                            if condition.field in ['followers', 'following', 'number_of_posts']:
                                field_category = 'numeric'
                            elif condition.field in ['interests', 'locations', 'links', 'phone_number']:
                                field_category = 'lists'
                            elif condition.field in ['is_verified', 'avoid']:
                                field_category = 'boolean'

                            condition_dict = {
                                'id': str(condition.id),
                                'field_category': field_category,
                                'field': condition.field,
                                'field_type': condition.field_type,
                                'operator': condition.operator,
                                'value': condition.value,
                                'required': condition.required
                            }
                            conditions.append(condition_dict)

                        # Add conditions to context
                        context['existing_conditions'] = json.dumps(conditions)
                        logger.info(f"Added {len(conditions)} conditions to context")
                    except CampaignTagRule.DoesNotExist:
                        logger.warning(f"Rule with ID {rule_id} not found")
                        context['existing_conditions'] = '[]'
                else:
                    logger.warning(f"No rule_id found in pattern data: {pattern_data}")
                    context['existing_conditions'] = '[]'
            except (json.JSONDecodeError, TypeError):
                logger.warning(f"Could not parse pattern as JSON: {self.object.pattern}")
                context['existing_conditions'] = '[]'
        else:
            # New tag, no existing conditions
            context['existing_conditions'] = '[]'

        return context

    def get_success_url(self):
        """
        Return the URL to redirect to after successful form submission.
        """
        return reverse('campaigns:dynamic_tag_list')

class DynamicTagUpdateView(UpdateView):
    """
    View to update an existing dynamic tag.
    """
    model = DynamicTag
    template_name = 'campaigns/dynamic_tag_form.html'
    form_class = DynamicTagForm

    def get_object(self, queryset=None):
        """
        Override get_object to handle the case when a tag doesn't exist.
        """
        import logging
        import sys
        from django.core.exceptions import ObjectDoesNotExist
        logger = logging.getLogger(__name__)

        # Set up console logging for immediate feedback
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        logger.setLevel(logging.DEBUG)

        try:
            # Get the tag by UUID
            obj = super().get_object(queryset)
            logger.critical(f"Found tag with ID {obj.id}, name: {obj.name}")
            return obj
        except ObjectDoesNotExist as e:
            logger.critical(f"Error getting tag: No Dynamic Tag found matching the query")
            # Return None if the tag doesn't exist
            return None
        except Exception as e:
            logger.critical(f"Unexpected error getting tag: {str(e)}")
            # Return None for any other exception
            return None

    def get(self, request, *args, **kwargs):
        """
        Override get to handle the case when a tag doesn't exist.
        """
        import logging
        import sys
        import traceback
        logger = logging.getLogger(__name__)

        # Set up console logging for immediate feedback
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        logger.setLevel(logging.DEBUG)

        logger.critical(f"DynamicTagUpdateView.get called with args: {args}, kwargs: {kwargs}")

        try:
            # Validate UUID format
            tag_uuid = kwargs.get('pk')
            logger.critical(f"Attempting to get tag with UUID: {tag_uuid}")

            # Get the tag object
            self.object = self.get_object()

            # Check if the tag exists
            if self.object is None:
                logger.critical("Tag not found, redirecting to tag list")
                messages.error(request, "Tag not found. It may have been deleted or never existed.")
                return redirect('campaigns:dynamic_tag_list')

            # Tag exists, proceed with normal flow
            logger.critical(f"Tag found, proceeding with normal flow: {self.object.name}")
            return super().get(request, *args, **kwargs)

        except Exception as e:
            # Log the exception
            logger.critical(f"Unexpected error in get method: {str(e)}")
            logger.critical(traceback.format_exc())

            # Redirect to tag list with error message
            messages.error(request, f"An error occurred: {str(e)}")
            return redirect('campaigns:dynamic_tag_list')

    def post(self, request, *args, **kwargs):
        """
        Override post to handle the case when a tag doesn't exist.
        """
        import logging
        import sys
        import traceback
        logger = logging.getLogger(__name__)

        # Set up console logging for immediate feedback
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        logger.setLevel(logging.DEBUG)

        logger.critical(f"DynamicTagUpdateView.post called with args: {args}, kwargs: {kwargs}")

        try:
            # Validate UUID format
            tag_uuid = kwargs.get('pk')
            logger.critical(f"Attempting to get tag with UUID: {tag_uuid}")

            # Log the request data for debugging
            logger.critical(f"Request POST data: {request.POST}")

            # Get the tag object
            self.object = self.get_object()

            # Check if the tag exists
            if self.object is None:
                logger.critical("Tag not found, redirecting to tag list")
                messages.error(request, "Tag not found. It may have been deleted or never existed.")
                return redirect('campaigns:dynamic_tag_list')

            # Tag exists, proceed with normal flow
            logger.critical(f"Tag found, proceeding with normal flow: {self.object.name}")
            return super().post(request, *args, **kwargs)

        except Exception as e:
            # Log the exception
            logger.critical(f"Unexpected error in post method: {str(e)}")
            logger.critical(traceback.format_exc())

            # Redirect to tag list with error message
            messages.error(request, f"An error occurred: {str(e)}")
            return redirect('campaigns:dynamic_tag_list')

    def get_form(self, form_class=None):
        """
        Get the form for this view.
        If the tag is a system tag, make certain fields read-only.
        """
        form = super().get_form(form_class)
        if self.object.is_system:
            form.fields['name'].widget.attrs['readonly'] = True
            form.fields['tag_type'].widget.attrs['readonly'] = True
            form.fields['field'].widget.attrs['readonly'] = True
            # Add a note about system tags
            for field in form.fields.values():
                if not hasattr(field, 'help_text') or not field.help_text:
                    field.help_text = ""
                field.help_text += " This is a system tag. Some fields cannot be modified."

        # Store the request in the form for later use
        form._request = self.request

        return form

    def get_context_data(self, **kwargs):
        """
        Add existing conditions to the context.
        """
        context = super().get_context_data(**kwargs)

        # If we're editing an existing tag, get its conditions
        if self.object and self.object.pk:
            import json
            import logging
            logger = logging.getLogger(__name__)

            try:
                # Make sure the pattern is a valid JSON string
                if not self.object.pattern or self.object.pattern.strip() == '':
                    pattern_data = {}
                else:
                    pattern_data = json.loads(self.object.pattern)

                if 'rule_id' in pattern_data:
                    from campaigns.models import CampaignTagRule, CampaignTagCondition
                    rule_id = pattern_data.get('rule_id')

                    try:
                        # Get the rule and its conditions
                        rule = CampaignTagRule.objects.get(id=rule_id)
                        conditions = []

                        for condition in rule.conditions.all():
                            # Convert condition to a dict for the template
                            field_category = 'text'  # Default

                            # Determine field category based on field
                            if condition.field in ['followers', 'following', 'number_of_posts']:
                                field_category = 'numeric'
                            elif condition.field in ['interests', 'locations', 'links', 'phone_number']:
                                field_category = 'lists'
                            elif condition.field in ['is_verified', 'avoid']:
                                field_category = 'boolean'

                            condition_dict = {
                                'id': str(condition.id),
                                'field_category': field_category,
                                'field': condition.field,
                                'field_type': condition.field_type,
                                'operator': condition.operator,
                                'value': condition.value,
                                'required': condition.required
                            }
                            conditions.append(condition_dict)

                        # Add conditions to context
                        context['existing_conditions'] = json.dumps(conditions)
                        logger.info(f"Added {len(conditions)} conditions to context")
                    except CampaignTagRule.DoesNotExist:
                        logger.warning(f"Rule with ID {rule_id} not found")
                        context['existing_conditions'] = '[]'
                else:
                    logger.warning(f"No rule_id found in pattern data: {pattern_data}")
                    context['existing_conditions'] = '[]'
            except (json.JSONDecodeError, TypeError):
                logger.warning(f"Could not parse pattern as JSON: {self.object.pattern}")
                context['existing_conditions'] = '[]'
        else:
            # New tag, no existing conditions
            context['existing_conditions'] = '[]'

        return context

    def get_success_url(self):
        """
        Return the URL to redirect to after successful form submission.
        """
        return reverse('campaigns:dynamic_tag_list')

    def form_valid(self, form):
        """
        If this is a system tag, ensure we don't change the system status.
        """
        import logging
        import json
        import traceback
        import sys
        from django.shortcuts import redirect
        logger = logging.getLogger(__name__)

        # Set up console logging for immediate feedback
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        logger.setLevel(logging.DEBUG)

        logger.critical("========== STARTING TAG UPDATE ==========")
        logger.critical(f"DynamicTagUpdateView form_valid - form.cleaned_data: {form.cleaned_data}")

        # Log the request data for debugging
        logger.critical(f"Request POST data: {self.request.POST}")
        logger.critical(f"conditions_json: {self.request.POST.get('conditions_json', 'NOT FOUND')}")

        if self.object.is_system:
            # Ensure system status doesn't change
            form.instance.is_system = True
            messages.info(self.request, f"The tag '{self.object.name}' is a system tag. Some fields cannot be modified.")

        # Ensure required fields have values
        if not form.instance.field:
            form.instance.field = 'bio'
            logger.warning("Field was empty, setting to 'bio'")

        if not form.instance.pattern:
            form.instance.pattern = '{}'  # Use empty JSON object instead of empty string
            logger.warning("Pattern was empty, setting to empty JSON object")

        if not form.instance.tag_type:
            form.instance.tag_type = 'keyword'
            logger.warning("Tag type was empty, setting to 'keyword'")

        # Set default values for any other required fields
        if not hasattr(form.instance, 'weight') or form.instance.weight is None:
            form.instance.weight = 1.0
            logger.warning("Weight was empty, setting to 1.0")

        try:
            # Get conditions_json from the request and add it to form.cleaned_data
            conditions_json = self.request.POST.get('conditions_json', '[]')
            logger.info(f"Processing conditions_json from request: {conditions_json}")

            # Make sure conditions_json is in form.cleaned_data
            if 'conditions_json' not in form.cleaned_data or not form.cleaned_data['conditions_json']:
                form.cleaned_data['conditions_json'] = conditions_json
                logger.info(f"Added conditions_json to form.cleaned_data: {conditions_json}")

            # Parse conditions to ensure they're valid
            try:
                conditions = json.loads(conditions_json)
                logger.info(f"Parsed conditions: {conditions}")

                # Add conditions to form.cleaned_data
                form.cleaned_data['conditions'] = conditions
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing conditions_json: {str(e)}")
                form.cleaned_data['conditions'] = []

            # Save the form first to update the tag
            logger.info("Saving form to update tag")
            tag = form.save(commit=True)  # Save the form and get the tag instance
            logger.info(f"Tag updated with ID: {tag.id}")

            # Now save the conditions
            if hasattr(form, 'save_conditions'):
                logger.info("Calling save_conditions method")
                success = form.save_conditions(tag)
                if success:
                    logger.info(f"Successfully saved conditions for tag '{tag.name}'")
                else:
                    logger.warning(f"Failed to save conditions for tag '{tag.name}'")
                    messages.warning(self.request, "Tag was updated but there was an issue saving the conditions.")
            else:
                logger.warning("Form does not have save_conditions method")

            # Verify the tag was saved correctly
            from campaigns.models import DynamicTag
            try:
                # Force a database refresh to ensure we're getting the latest data
                saved_tag = DynamicTag.objects.get(id=tag.id)
                logger.info(f"Tag verification - Found tag with ID {saved_tag.id}, name: {saved_tag.name}")

                # Log all tags in the database for debugging
                all_tags = DynamicTag.objects.all()
                logger.info(f"All tags in database: {[t.name for t in all_tags]}")

                # Double-check that our tag is in the list
                if tag.id in [t.id for t in all_tags]:
                    logger.info(f"Tag {tag.id} confirmed in database")
                else:
                    logger.error(f"Tag {tag.id} NOT found in all_tags list!")

            except DynamicTag.DoesNotExist:
                logger.error(f"Tag verification failed - Could not find tag with ID {tag.id}")
                messages.error(self.request, "Error updating tag: Tag was not saved correctly.")
                return self.form_invalid(form)

            messages.success(self.request, f"Tag '{tag.name}' updated successfully.")

            # Explicitly redirect to the tag list page
            logger.critical("========== REDIRECTING TO TAG LIST PAGE ==========")
            from django.http import HttpResponseRedirect
            from django.urls import reverse

            # Get the absolute URL for the tag list page
            tag_list_url = reverse('campaigns:dynamic_tag_list')
            logger.critical(f"Redirecting to tag list URL: {tag_list_url}")

            # Try a different approach to redirect
            try:
                # First, try using HttpResponseRedirect with the absolute URL
                logger.critical("Using HttpResponseRedirect for redirect")
                response = HttpResponseRedirect(tag_list_url)

                # Add debugging headers
                response['X-Debug-Info'] = 'Tag updated successfully'
                response['X-Tag-ID'] = str(tag.id)
                response['X-Tag-Name'] = tag.name

                # Log the response for debugging
                logger.critical(f"Response created: {response}")
                logger.critical(f"Response status code: {response.status_code}")
                logger.critical(f"Response headers: {response.headers}")

                return response
            except Exception as e:
                logger.critical(f"Error during redirect: {str(e)}")
                logger.critical(traceback.format_exc())

                # As a fallback, try a simple redirect
                logger.critical("Falling back to simple redirect")
                return redirect('campaigns:dynamic_tag_list')
        except Exception as e:
            logger.exception(f"Error updating tag: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            messages.error(self.request, f"Error updating tag: {str(e)}")
            return self.form_invalid(form)

class DynamicTagDeleteView(DeleteView):
    """
    View to delete a dynamic tag.
    """
    model = DynamicTag
    template_name = 'campaigns/dynamic_tag_confirm_delete.html'
    success_url = reverse_lazy('campaigns:dynamic_tag_list')

    def get(self, request, *args, **kwargs):
        """
        Check if the tag is a system tag before showing the delete confirmation page.
        """
        self.object = self.get_object()
        if self.object.is_system:
            messages.error(request, f"The tag '{self.object.name}' is a system tag and cannot be deleted.")
            return redirect('campaigns:dynamic_tag_list')
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        """
        Check if the tag is a system tag before deleting it.
        """
        self.object = self.get_object()
        if self.object.is_system:
            messages.error(request, f"The tag '{self.object.name}' is a system tag and cannot be deleted.")
            return redirect('campaigns:dynamic_tag_list')
        return super().post(request, *args, **kwargs)

class TagGroupListView(ListView):
    """
    View to list all tag groups.
    """
    model = TagGroup
    template_name = 'campaigns/tag_group_list.html'
    context_object_name = 'tag_groups'

class TagGroupCreateView(CreateView):
    """
    View to create a new tag group.
    """
    model = TagGroup
    template_name = 'campaigns/tag_group_form.html'
    fields = ['name', 'description', 'color', 'is_global']

    def get_success_url(self):
        """
        Return the URL to redirect to after successful form submission.
        """
        return reverse('campaigns:tag_group_list')

    def form_valid(self, form):
        """
        Process the form submission.
        """
        # Set the creator to the current user if authenticated, otherwise set to None
        if self.request.user.is_authenticated:
            form.instance.creator = self.request.user

        # Ensure color field has a value if not provided
        if not form.instance.color:
            form.instance.color = '#6c757d'  # Default color

        # Set priority field explicitly (legacy field that should be removed)
        form.instance.priority = 0

        try:
            response = super().form_valid(form)
            messages.success(self.request, f"Tag group '{form.instance.name}' created successfully.")
            return response
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.exception(f"Error creating tag group: {str(e)}")
            messages.error(self.request, f"Error creating tag group: {str(e)}")
            return self.form_invalid(form)

class TagGroupDetailView(DetailView):
    """
    View to display tag group details.
    """
    model = TagGroup
    template_name = 'campaigns/tag_group_detail.html'
    context_object_name = 'tag_group'

    def get_context_data(self, **kwargs):
        """
        Add additional context data for the tag group detail view.
        """
        context = super().get_context_data(**kwargs)
        tag_group = self.object

        # Get tags that are already in this group
        tags_in_group = tag_group.tags.all().select_related('category').order_by('name')
        context['tags'] = tags_in_group

        # Get all available tags (since tags can now belong to multiple groups)
        from campaigns.models import DynamicTag
        available_tags = DynamicTag.objects.all().select_related('category').order_by('name')
        context['available_tags'] = available_tags

        # Get all categories for filtering
        from campaigns.models import TagCategory
        context['categories'] = TagCategory.objects.all().order_by('name')

        return context

class TagGroupUpdateView(UpdateView):
    """
    View to update an existing tag group.
    """
    model = TagGroup
    template_name = 'campaigns/tag_group_form.html'
    fields = ['name', 'description', 'color', 'is_global']

    def get_success_url(self):
        """
        Return the URL to redirect to after successful form submission.
        """
        return reverse('campaigns:tag_group_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        """
        Process the form submission.
        """
        # Ensure color field has a value if not provided
        if not form.instance.color:
            form.instance.color = '#6c757d'  # Default color

        # Set priority field explicitly (legacy field that should be removed)
        form.instance.priority = 0

        try:
            response = super().form_valid(form)
            messages.success(self.request, f"Tag group '{form.instance.name}' updated successfully.")
            return response
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.exception(f"Error updating tag group: {str(e)}")
            messages.error(self.request, f"Error updating tag group: {str(e)}")
            return self.form_invalid(form)

class TagGroupDeleteView(DeleteView):
    """
    View to delete a tag group.
    """
    model = TagGroup
    template_name = 'campaigns/tag_group_confirm_delete.html'
    success_url = reverse_lazy('campaigns:tag_group_list')
    context_object_name = 'tag_group'

    def get_context_data(self, **kwargs):
        """
        Add additional context data for the tag group delete view.
        """
        context = super().get_context_data(**kwargs)
        # Ensure the tag_group object is available in the template context
        context['tag_group'] = self.object
        return context

class AddTagToGroupView(View):
    """
    View to add tags to a tag group.
    """
    def post(self, request, pk):
        """
        Process the form submission.
        """
        import logging
        logger = logging.getLogger(__name__)

        tag_group = get_object_or_404(TagGroup, pk=pk)
        tag_ids = request.POST.getlist('tag_id')

        if not tag_ids:
            messages.error(request, "Please select at least one tag to add to the group.")
            return redirect('campaigns:tag_group_detail', pk=tag_group.pk)

        try:
            from campaigns.models import DynamicTag
            added_count = 0
            already_in_group_count = 0

            for tag_id in tag_ids:
                tag = get_object_or_404(DynamicTag, pk=tag_id)

                # Check if tag is already in this group
                if tag_group in tag.tag_groups.all():
                    already_in_group_count += 1
                    logger.info(f"Tag '{tag.name}' is already in group '{tag_group.name}'")
                else:
                    # Add tag to the group
                    tag.tag_groups.add(tag_group)
                    added_count += 1
                    logger.info(f"Tag '{tag.name}' added to group '{tag_group.name}'")

            # Provide feedback based on results
            if added_count > 0:
                if added_count == 1:
                    messages.success(request, f"1 tag has been successfully added to group '{tag_group.name}'.")
                else:
                    messages.success(request, f"{added_count} tags have been successfully added to group '{tag_group.name}'.")

            if already_in_group_count > 0:
                if already_in_group_count == 1:
                    messages.info(request, f"1 tag was already in group '{tag_group.name}'.")
                else:
                    messages.info(request, f"{already_in_group_count} tags were already in group '{tag_group.name}'.")

        except Exception as e:
            logger.error(f"Error adding tags to group: {str(e)}")
            messages.error(request, f"Error adding tags to group: {str(e)}")

        return redirect('campaigns:tag_group_detail', pk=tag_group.pk)

class RemoveTagFromGroupView(View):
    """
    View to remove a tag from a tag group.
    Supports both regular form submissions and HTMX requests.
    """
    def post(self, request, group_pk, tag_pk):
        """
        Process the form submission.
        """
        import logging
        from django.http import JsonResponse
        logger = logging.getLogger(__name__)

        try:
            tag_group = get_object_or_404(TagGroup, pk=group_pk)
            tag = get_object_or_404(DynamicTag, pk=tag_pk)

            # Check if tag is actually in the group
            if not tag.tag_groups.filter(pk=group_pk).exists():
                error_msg = f"Tag '{tag.name}' is not in group '{tag_group.name}'."
                logger.warning(error_msg)

                if request.headers.get('HX-Request'):
                    return JsonResponse({'error': error_msg}, status=400)
                else:
                    messages.error(request, error_msg)
                    return redirect('campaigns:tag_group_detail', pk=tag_group.pk)

            # Remove tag from the group using many-to-many relationship
            tag.tag_groups.remove(tag_group)
            success_msg = f"Tag '{tag.name}' removed from group '{tag_group.name}'."
            logger.info(success_msg)

            # Handle HTMX requests
            if request.headers.get('HX-Request'):
                return JsonResponse({
                    'success': True,
                    'message': success_msg,
                    'tag_id': str(tag.id),
                    'group_id': str(tag_group.id)
                })
            else:
                # Handle regular form submissions
                messages.success(request, success_msg)
                return redirect('campaigns:tag_group_detail', pk=tag_group.pk)

        except TagGroup.DoesNotExist:
            error_msg = "Tag group not found."
            logger.error(f"Tag group not found: {group_pk}")

            if request.headers.get('HX-Request'):
                return JsonResponse({'error': error_msg}, status=404)
            else:
                messages.error(request, error_msg)
                return redirect('campaigns:tag_group_list')

        except DynamicTag.DoesNotExist:
            error_msg = "Tag not found."
            logger.error(f"Tag not found: {tag_pk}")

            if request.headers.get('HX-Request'):
                return JsonResponse({'error': error_msg}, status=404)
            else:
                messages.error(request, error_msg)
                return redirect('campaigns:tag_group_detail', pk=group_pk)

        except Exception as e:
            error_msg = f"Error removing tag from group: {str(e)}"
            logger.exception(error_msg)

            if request.headers.get('HX-Request'):
                return JsonResponse({'error': error_msg}, status=500)
            else:
                messages.error(request, error_msg)
                return redirect('campaigns:tag_group_detail', pk=group_pk)

    def get(self, request, group_pk, tag_pk):
        """
        Handle GET requests by redirecting to the tag group detail page.
        This prevents the HTTP 405 error when someone accidentally uses GET.
        """
        import logging
        logger = logging.getLogger(__name__)

        logger.warning(f"GET request received for remove tag endpoint. Redirecting to tag group detail.")
        messages.warning(request, "Tag removal requires a POST request. Please use the remove button.")

        return redirect('campaigns:tag_group_detail', pk=group_pk)

class LocationSearchView(View):
    """
    View to search for locations.
    """
    def get(self, request):
        """
        Process the request.
        """
        query = request.GET.get('q', '')
        limit = int(request.GET.get('limit', 10))

        if not query:
            return JsonResponse({'results': []})

        # Import the location service
        from campaigns.services.legacy.location_service import LocationService

        # Search for locations
        location_service = LocationService()
        locations = location_service.search_locations(query)

        # Check if the query is a numeric value (potential location ID)
        is_numeric_search = query.isdigit()

        # If it's a numeric search, prioritize exact ID matches
        if is_numeric_search:
            # Sort locations to prioritize exact ID matches
            locations.sort(key=lambda loc: 0 if query == loc['location_id'] else 1)

        # Format the results
        results = []
        for location in locations[:limit]:
            # Include location ID in the label for better identification
            # Highlight the ID if it's a numeric search
            if is_numeric_search and query in location['location_id']:
                results.append({
                    'id': location['location_id'],
                    'label': f"{location['city']}, {location['country']} (ID: {location['location_id']})",
                    'display_label': f"{location['city']}, {location['country']}",
                    'city': location['city'],
                    'country': location['country'],
                    'highlight_id': True
                })
            else:
                results.append({
                    'id': location['location_id'],
                    'label': f"{location['city']}, {location['country']} (ID: {location['location_id']})",
                    'display_label': f"{location['city']}, {location['country']}",
                    'city': location['city'],
                    'country': location['country'],
                    'highlight_id': False
                })

        return JsonResponse({'results': results})

class LocationCountriesView(View):
    """
    View to get a list of countries.
    """
    def get(self, request):
        """
        Process the request.
        """
        # Get countries logic here
        countries = []  # Replace with actual countries
        return JsonResponse({'countries': countries})

class AnalyzeAccountsAPIView(View):
    """
    API view to analyze accounts.
    """
    def post(self, request):
        """
        Process the request.
        """
        # Analysis logic here
        return JsonResponse({'status': 'success'})

# Import render for the view functions
from django.shortcuts import render

class CheckTagExistsView(View):
    """
    API view to check if a tag exists in the database.
    """
    def get(self, request):
        """
        Process the request.
        """
        tag_id = request.GET.get('tag_id', '')

        if not tag_id:
            return JsonResponse({'exists': False, 'error': 'No tag ID provided'})

        try:
            # Check if the tag exists in the database
            tag = DynamicTag.objects.filter(pk=tag_id).exists()
            return JsonResponse({'exists': tag})
        except Exception as e:
            # Log the error
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error checking if tag exists: {str(e)}")

            # Return a response
            return JsonResponse({'exists': False, 'error': str(e)})
